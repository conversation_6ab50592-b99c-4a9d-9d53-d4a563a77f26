<?php

namespace Database\Seeders;

use App\Models\CabangLomba;
use App\Models\Golongan;
use App\Models\Mimbar;
use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class MtqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Wilayah
        $lampung = Wilayah::create([
            'kode_wilayah' => 'LP',
            'nama_wilayah' => 'Lampung',
            'level_wilayah' => 'provinsi',
            'parent_id' => null,
            'status' => 'aktif'
        ]);

        $bandarLampung = Wilayah::create([
            'kode_wilayah' => 'BL',
            'nama_wilayah' => 'Bandar Lampung',
            'level_wilayah' => 'kota',
            'parent_id' => $lampung->id_wilayah,
            'status' => 'aktif'
        ]);

        $lampungSelatan = Wilayah::create([
            'kode_wilayah' => 'LS',
            'nama_wilayah' => 'Lampung Selatan',
            'level_wilayah' => 'kabupaten',
            'parent_id' => $lampung->id_wilayah,
            'status' => 'aktif'
        ]);

        // Seed Users
        $superAdmin = User::create([
            'username' => 'superadmin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'superadmin',
            'id_wilayah' => $lampung->id_wilayah,
            'nama_lengkap' => 'Super Administrator',
            'no_telepon' => '081234567890',
            'status' => 'aktif'
        ]);

        $admin = User::create([
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'id_wilayah' => $lampung->id_wilayah,
            'nama_lengkap' => 'Administrator',
            'no_telepon' => '081234567891',
            'status' => 'aktif',
            'created_by' => $superAdmin->id_user
        ]);

        $adminDaerah = User::create([
            'username' => 'admin_bl',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin_daerah',
            'id_wilayah' => $bandarLampung->id_wilayah,
            'nama_lengkap' => 'Admin Bandar Lampung',
            'no_telepon' => '081234567892',
            'status' => 'aktif',
            'created_by' => $admin->id_user
        ]);

        // Seed Cabang Lomba
        $tilawah = CabangLomba::create([
            'kode_cabang' => 'TIL',
            'nama_cabang' => 'Tilawah',
            'deskripsi' => 'Cabang lomba tilawah Al-Quran',
            'status' => 'aktif'
        ]);

        $tahfidz = CabangLomba::create([
            'kode_cabang' => 'THF',
            'nama_cabang' => 'Tahfidz',
            'deskripsi' => 'Cabang lomba hafalan Al-Quran',
            'status' => 'aktif'
        ]);

        // Seed Golongan
        Golongan::create([
            'kode_golongan' => 'TIL-A-L',
            'nama_golongan' => 'Tilawah Anak Laki-laki',
            'id_cabang' => $tilawah->id_cabang,
            'jenis_kelamin' => 'L',
            'batas_umur_min' => 7,
            'batas_umur_max' => 12,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 100000,
            'nomor_urut_awal' => 1,
            'nomor_urut_akhir' => 50,
            'status' => 'aktif'
        ]);

        Golongan::create([
            'kode_golongan' => 'TIL-A-P',
            'nama_golongan' => 'Tilawah Anak Perempuan',
            'id_cabang' => $tilawah->id_cabang,
            'jenis_kelamin' => 'P',
            'batas_umur_min' => 7,
            'batas_umur_max' => 12,
            'kuota_max' => 50,
            'biaya_pendaftaran' => 100000,
            'nomor_urut_awal' => 51,
            'nomor_urut_akhir' => 100,
            'status' => 'aktif'
        ]);

        // Seed Mimbar
        Mimbar::create([
            'kode_mimbar' => 'M001',
            'nama_mimbar' => 'Mimbar Utama',
            'keterangan' => 'Mimbar utama untuk lomba',
            'kapasitas' => 100,
            'status' => 'aktif'
        ]);

        Mimbar::create([
            'kode_mimbar' => 'M002',
            'nama_mimbar' => 'Mimbar Cadangan',
            'keterangan' => 'Mimbar cadangan untuk lomba',
            'kapasitas' => 50,
            'status' => 'aktif'
        ]);
    }
}
