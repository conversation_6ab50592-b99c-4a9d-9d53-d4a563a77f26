import type { LucideIcon } from 'lucide-vue-next';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
}

export type AppPageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
};

export interface User {
    id_user: number;
    username: string;
    email: string;
    role: 'superadmin' | 'admin' | 'admin_daerah' | 'peserta' | 'dewan_hakim';
    id_wilayah: number | null;
    nama_lengkap: string;
    no_telepon: string | null;
    status: 'aktif' | 'non_aktif' | 'suspended';
    last_login: string | null;
    created_by: number | null;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;
