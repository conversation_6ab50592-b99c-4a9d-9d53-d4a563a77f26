<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as="link"
          :href="route('admin.peserta.index')"
          variant="ghost"
          size="sm"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Ke<PERSON><PERSON>
        </Button>
        <Heading>Edit Peserta: {{ peserta.nama_lengkap }}</Heading>
      </div>
    </template>

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Peserta</CardTitle>
          <CardDescription>
            Perbarui informasi peserta di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Account Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Informasi Akun</h3>
                
                <div>
                  <Label for="username" required>Username</Label>
                  <Input
                    id="username"
                    v-model="form.username"
                    type="text"
                    :error="form.errors.username"
                    placeholder="Masukkan username"
                    class="mt-1"
                    required
                  />
                  <InputError :message="form.errors.username" />
                </div>

                <div>
                  <Label for="email" required>Email</Label>
                  <Input
                    id="email"
                    v-model="form.email"
                    type="email"
                    :error="form.errors.email"
                    placeholder="Masukkan email"
                    class="mt-1"
                    required
                  />
                  <InputError :message="form.errors.email" />
                </div>

                <div>
                  <Label for="status_peserta" required>Status Peserta</Label>
                  <select
                    id="status_peserta"
                    v-model="form.status_peserta"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  >
                    <option value="draft">Draft</option>
                    <option value="submitted">Disubmit</option>
                    <option value="verified">Diverifikasi</option>
                    <option value="approved">Disetujui</option>
                    <option value="rejected">Ditolak</option>
                  </select>
                  <InputError :message="form.errors.status_peserta" />
                </div>
              </div>

              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Informasi Pribadi</h3>
                
                <div>
                  <Label for="nik" required>NIK</Label>
                  <Input
                    id="nik"
                    v-model="form.nik"
                    type="text"
                    :error="form.errors.nik"
                    placeholder="Masukkan NIK (16 digit)"
                    maxlength="16"
                    class="mt-1"
                    required
                  />
                  <InputError :message="form.errors.nik" />
                </div>

                <div>
                  <Label for="nama_lengkap" required>Nama Lengkap</Label>
                  <Input
                    id="nama_lengkap"
                    v-model="form.nama_lengkap"
                    type="text"
                    :error="form.errors.nama_lengkap"
                    placeholder="Masukkan nama lengkap"
                    class="mt-1"
                    required
                  />
                  <InputError :message="form.errors.nama_lengkap" />
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <Label for="tempat_lahir" required>Tempat Lahir</Label>
                    <Input
                      id="tempat_lahir"
                      v-model="form.tempat_lahir"
                      type="text"
                      :error="form.errors.tempat_lahir"
                      placeholder="Kota lahir"
                      class="mt-1"
                      required
                    />
                    <InputError :message="form.errors.tempat_lahir" />
                  </div>

                  <div>
                    <Label for="tanggal_lahir" required>Tanggal Lahir</Label>
                    <Input
                      id="tanggal_lahir"
                      v-model="form.tanggal_lahir"
                      type="date"
                      :error="form.errors.tanggal_lahir"
                      class="mt-1"
                      required
                    />
                    <InputError :message="form.errors.tanggal_lahir" />
                  </div>
                </div>

                <div>
                  <Label for="jenis_kelamin" required>Jenis Kelamin</Label>
                  <select
                    id="jenis_kelamin"
                    v-model="form.jenis_kelamin"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  >
                    <option value="">Pilih jenis kelamin</option>
                    <option value="L">Laki-laki</option>
                    <option value="P">Perempuan</option>
                  </select>
                  <InputError :message="form.errors.jenis_kelamin" />
                </div>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Informasi Kontak</h3>
              
              <div>
                <Label for="alamat" required>Alamat</Label>
                <textarea
                  id="alamat"
                  v-model="form.alamat"
                  rows="3"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  placeholder="Masukkan alamat lengkap"
                  required
                ></textarea>
                <InputError :message="form.errors.alamat" />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="id_wilayah" required>Wilayah</Label>
                  <select
                    id="id_wilayah"
                    v-model="form.id_wilayah"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    required
                  >
                    <option value="">Pilih wilayah</option>
                    <option v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah">
                      {{ w.nama_wilayah }}
                    </option>
                  </select>
                  <InputError :message="form.errors.id_wilayah" />
                </div>

                <div>
                  <Label for="no_telepon">No. Telepon</Label>
                  <Input
                    id="no_telepon"
                    v-model="form.no_telepon"
                    type="tel"
                    :error="form.errors.no_telepon"
                    placeholder="Masukkan nomor telepon"
                    class="mt-1"
                  />
                  <InputError :message="form.errors.no_telepon" />
                </div>
              </div>
            </div>

            <!-- Family Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Informasi Keluarga</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="nama_ayah">Nama Ayah</Label>
                  <Input
                    id="nama_ayah"
                    v-model="form.nama_ayah"
                    type="text"
                    :error="form.errors.nama_ayah"
                    placeholder="Masukkan nama ayah"
                    class="mt-1"
                  />
                  <InputError :message="form.errors.nama_ayah" />
                </div>

                <div>
                  <Label for="nama_ibu">Nama Ibu</Label>
                  <Input
                    id="nama_ibu"
                    v-model="form.nama_ibu"
                    type="text"
                    :error="form.errors.nama_ibu"
                    placeholder="Masukkan nama ibu"
                    class="mt-1"
                  />
                  <InputError :message="form.errors.nama_ibu" />
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Informasi Tambahan</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="pekerjaan">Pekerjaan</Label>
                  <Input
                    id="pekerjaan"
                    v-model="form.pekerjaan"
                    type="text"
                    :error="form.errors.pekerjaan"
                    placeholder="Masukkan pekerjaan"
                    class="mt-1"
                  />
                  <InputError :message="form.errors.pekerjaan" />
                </div>

                <div>
                  <Label for="instansi_asal">Instansi Asal</Label>
                  <Input
                    id="instansi_asal"
                    v-model="form.instansi_asal"
                    type="text"
                    :error="form.errors.instansi_asal"
                    placeholder="Masukkan instansi asal"
                    class="mt-1"
                  />
                  <InputError :message="form.errors.instansi_asal" />
                </div>
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                as="link"
                :href="route('admin.peserta.index')"
                variant="outline"
              >
                Batal
              </Button>
              <Button
                type="submit"
                variant="primary"
                :disabled="form.processing"
              >
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                {{ form.processing ? 'Menyimpan...' : 'Perbarui Peserta' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardDescription from '@/components/ui/card/CardDescription.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

interface Peserta {
  id_peserta: number
  nik: string
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  id_wilayah: number
  no_telepon: string | null
  email: string | null
  nama_ayah: string | null
  nama_ibu: string | null
  pekerjaan: string | null
  instansi_asal: string | null
  status_peserta: string
  user: {
    username: string
    email: string
  }
}

const props = defineProps<{
  peserta: Peserta
  wilayah: Wilayah[]
}>()

const form = useForm({
  username: props.peserta.user.username,
  email: props.peserta.user.email,
  nik: props.peserta.nik,
  nama_lengkap: props.peserta.nama_lengkap,
  tempat_lahir: props.peserta.tempat_lahir,
  tanggal_lahir: props.peserta.tanggal_lahir,
  jenis_kelamin: props.peserta.jenis_kelamin,
  alamat: props.peserta.alamat,
  id_wilayah: props.peserta.id_wilayah,
  no_telepon: props.peserta.no_telepon || '',
  nama_ayah: props.peserta.nama_ayah || '',
  nama_ibu: props.peserta.nama_ibu || '',
  pekerjaan: props.peserta.pekerjaan || '',
  instansi_asal: props.peserta.instansi_asal || '',
  status_peserta: props.peserta.status_peserta
})

function submit() {
  form.put(route('admin.peserta.update', props.peserta.id_peserta), {
    onSuccess: () => {
      // Success message will be handled by the backend
    }
  })
}
</script>
