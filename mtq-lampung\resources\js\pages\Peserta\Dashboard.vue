<template>
  <AppLayout>
    <template #header>
      <Heading>Dashboard Peserta</Heading>
    </template>

    <div class="space-y-6">
      <!-- Welcome Message -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0 h-12 w-12">
              <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
                <span class="text-lg font-medium text-white">
                  {{ getInitials($page.props.auth.user.nama_lengkap) }}
                </span>
              </div>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-gray-900">
                Selamat datang, {{ $page.props.auth.user.nama_lengkap }}!
              </h2>
              <p class="text-gray-600">
                Kelola profil dan pendaftaran lomba MTQ Anda di sini.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card class="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="user" class="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">Profil Saya</h3>
                <p class="text-sm text-gray-500">Kelola informasi pribadi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="file-text" class="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">Pendaftaran Lomba</h3>
                <p class="text-sm text-gray-500">Daftar cabang lomba</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="folder" class="h-8 w-8 text-purple-600" />
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">Dokumen</h3>
                <p class="text-sm text-gray-500">Upload dokumen persyaratan</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Status Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Registration Status -->
        <Card>
          <CardHeader>
            <CardTitle>Status Pendaftaran</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <Icon name="user-check" class="h-5 w-5 text-blue-600" />
                  <span class="text-sm font-medium text-blue-900">Profil Lengkap</span>
                </div>
                <Icon name="check-circle" class="h-5 w-5 text-green-600" />
              </div>
              
              <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <Icon name="file-text" class="h-5 w-5 text-yellow-600" />
                  <span class="text-sm font-medium text-yellow-900">Pendaftaran Lomba</span>
                </div>
                <Icon name="clock" class="h-5 w-5 text-yellow-600" />
              </div>
              
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <Icon name="folder" class="h-5 w-5 text-gray-600" />
                  <span class="text-sm font-medium text-gray-900">Upload Dokumen</span>
                </div>
                <Icon name="x-circle" class="h-5 w-5 text-gray-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Important Information -->
        <Card>
          <CardHeader>
            <CardTitle>Informasi Penting</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-start space-x-3">
                  <Icon name="info" class="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 class="text-sm font-medium text-green-900">Pendaftaran Gratis</h4>
                    <p class="text-sm text-green-700 mt-1">
                      Pendaftaran MTQ Lampung tahun ini tidak dikenakan biaya.
                    </p>
                  </div>
                </div>
              </div>
              
              <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start space-x-3">
                  <Icon name="calendar" class="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 class="text-sm font-medium text-blue-900">Batas Pendaftaran</h4>
                    <p class="text-sm text-blue-700 mt-1">
                      Pastikan melengkapi semua dokumen sebelum batas waktu.
                    </p>
                  </div>
                </div>
              </div>
              
              <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <div class="flex items-start space-x-3">
                  <Icon name="help-circle" class="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <h4 class="text-sm font-medium text-purple-900">Butuh Bantuan?</h4>
                    <p class="text-sm text-purple-700 mt-1">
                      Hubungi admin daerah atau panitia jika ada pertanyaan.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Recent Activity -->
      <Card>
        <CardHeader>
          <CardTitle>Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div class="flex-shrink-0">
                <Icon name="user-plus" class="h-5 w-5 text-green-600" />
              </div>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">Akun berhasil dibuat</p>
                <p class="text-sm text-gray-500">Selamat datang di sistem MTQ Lampung</p>
              </div>
              <div class="text-sm text-gray-500">
                Hari ini
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import Icon from '@/components/Icon.vue'

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
</script>
