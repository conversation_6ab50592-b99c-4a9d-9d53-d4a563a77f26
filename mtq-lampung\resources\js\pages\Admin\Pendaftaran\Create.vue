<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Label from '@/components/ui/label/Label.vue'
import Select from '@/components/ui/select/Select.vue'
import SelectContent from '@/components/ui/select/SelectContent.vue'
import SelectItem from '@/components/ui/select/SelectItem.vue'
import SelectTrigger from '@/components/ui/select/SelectTrigger.vue'
import SelectValue from '@/components/ui/select/SelectValue.vue'
import Card from '@/components/ui/card/Card.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import CardDescription from '@/components/ui/card/CardDescription.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface Peserta {
  id_peserta: number
  nama_lengkap: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
  cabang_lomba: {
    nama_cabang: string
  }
}

interface Mimbar {
  id_mimbar: number
  nama_mimbar: string
}

const props = defineProps<{
  peserta: Peserta[]
  golongan: Golongan[]
  mimbar: Mimbar[]
}>()

const form = useForm({
  id_peserta: '',
  id_golongan: '',
  id_mimbar: ''
})

function submit() {
  form.post(route('admin.pendaftaran.store'), {
    onSuccess: () => {
      // Success message will be handled by the backend
    }
  })
}

</script>

<template>
    <AppLayout>
        <Head title="Tambah Pendaftaran" />
        <div class="space-y-6">
            <div class="flex items-center space-x-3">
                <Button as-child class="text-muted-foreground bg-gray-100 cursor-pointer hover:text-foreground">
                    <TextLink :href="route('admin.pendaftaran.index')">
                        <Icon name="arrowLeft" class="w-4 h-4 mr-2" />
                        Kembali
                    </TextLink>
                    </Button>
                <div>
                    <Heading title="Tambah Pendaftaran" description="Lengkapi informasi pendaftaran di bawah ini" />
                </div>
            </div>

            <!-- Form Container -->
            <div class="max-w-5xl mx-auto">
                <form @submit.prevent="submit" class="space-y-4">
                    <!-- Peserta Information -->
                    <Card>
                        <CardHeader class="pb-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-blue-500/10 rounded-full flex items-center justify-center">
                                    <Icon name="user" class="w-3 h-3 text-blue-500" />
                                </div>
                                <div>
                                    <CardTitle class="text-base">Informasi Peserta</CardTitle>
                                    <CardDescription class="text-xs">
                                        Data peserta yang mendaftar
                                    </CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent class="space-y-3">
                            <div class="space-y-1">
                                <Label for="id_peserta" class="text-xs font-medium">
                                    Peserta <span class="text-red-500">*</span>
                                </Label>
                                <Select v-model="form.id_peserta" required>
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Pilih Peserta" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="p in peserta" :key="p.id_peserta" :value="p.id_peserta.toString()">
                                            {{ p.nama_lengkap }}
                                        </SelectItem>
                                    </SelectContent>
                                    <InputError :message="form.errors.id_peserta" />
                                </Select>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-4 pt-6 border-t">
                        <Button
                            as="link"
                            :href="route('admin.pendaftaran.index')"
                            variant="outline"
                        >
                            Batal
                        </Button>
                        <Button
                            type="submit"
                            variant="primary"
                            :disabled="form.processing"
                        >
                            <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                            {{ form.processing ? 'Menyimpan...' : 'Simpan Pendaftaran' }}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>

</template>
