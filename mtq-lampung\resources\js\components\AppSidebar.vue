<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type User } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { BookOpen, Folder, LayoutGrid, Users, Settings, FileText, Trophy, UserCheck, BarChart3 } from 'lucide-vue-next';
import { computed } from 'vue';
import AppLogo from './AppLogo.vue';

const page = usePage();
const user = page.props.auth.user as User;

const mainNavItems = computed((): NavItem[] => {
    const baseItems: NavItem[] = [];

    // Admin navigation items
    if (['superadmin', 'admin', 'admin_daerah'].includes(user.role)) {
        baseItems.push(
            {
                title: 'Dashboard Admin',
                href: '/admin/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Manajemen Peserta',
                href: '/admin/peserta',
                icon: Users,
            },
            {
                title: 'Pendaftaran Lomba',
                href: '/admin/pendaftaran',
                icon: FileText,
            },
            {
                title: 'Cabang Lomba',
                href: '/admin/cabang-lomba',
                icon: Trophy,
            }
        );

        // Super admin and admin only
        if (['superadmin', 'admin'].includes(user.role)) {
            baseItems.push(
                {
                    title: 'Manajemen User',
                    href: '/admin/users',
                    icon: UserCheck,
                },
                {
                    title: 'Laporan',
                    href: '/admin/laporan',
                    icon: BarChart3,
                },
                {
                    title: 'Pengaturan',
                    href: '/admin/pengaturan',
                    icon: Settings,
                }
            );
        }
    }

    // Peserta navigation items
    if (user.role === 'peserta') {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/peserta/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Profil Saya',
                href: '/peserta/profil',
                icon: Users,
            },
            {
                title: 'Pendaftaran Lomba',
                href: '/peserta/pendaftaran',
                icon: FileText,
            },
            {
                title: 'Dokumen',
                href: '/peserta/dokumen',
                icon: Folder,
            }
        );
    }

    // Dewan Hakim navigation items
    if (user.role === 'dewan_hakim') {
        baseItems.push(
            {
                title: 'Dashboard',
                href: '/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Penilaian',
                href: '/hakim/penilaian',
                icon: Trophy,
            }
        );
    }

    return baseItems;
});

const footerNavItems: NavItem[] = [
    {
        title: 'MTQ Lampung',
        href: 'https://mtqlampung.id',
        icon: Folder,
    },
    {
        title: 'Bantuan',
        href: '/bantuan',
        icon: BookOpen,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
