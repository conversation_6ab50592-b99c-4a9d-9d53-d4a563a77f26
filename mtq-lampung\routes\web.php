<?php

use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\PesertaController;
use App\Http\Controllers\Admin\PendaftaranController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin Routes
Route::middleware(['auth', 'role:superadmin,admin,admin_daerah'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::resource('peserta', PesertaController::class);
    Route::resource('pendaftaran', PendaftaranController::class);
});

// Peserta Routes
Route::middleware(['auth', 'role:peserta'])->prefix('peserta')->name('peserta.')->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Peserta/Dashboard');
    })->name('dashboard');
});

// Redirect authenticated users based on role
Route::middleware(['auth'])->get('/redirect', function () {
    $user = Auth::user();

    switch ($user->role) {
        case 'superadmin':
        case 'admin':
        case 'admin_daerah':
            return redirect()->route('admin.dashboard');
        case 'peserta':
            return redirect()->route('peserta.dashboard');
        case 'dewan_hakim':
            return redirect()->route('dashboard'); // Default dashboard for now
        default:
            return redirect()->route('dashboard');
    }
})->name('redirect');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
